openapi: 3.0.0
info:
  title: 排课管理API更新 - 支持24小时排课模式
  description: 更新排课管理相关接口，新增24小时排课模式支持
  version: 1.1.0
  contact:
    name: 助教排课系统
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 开发环境
  - url: https://api.teachingassistant.com
    description: 生产环境

paths:
  /principal/courses/available-slots:
    get:
      summary: 获取空闲时段（校长端）- 支持24小时排课模式
      description: |
        根据教师ID、教室ID和日期范围获取空闲时段。
        新增排课模式参数，支持标准模式（8:00-12:00, 14:00-18:00）和24小时模式（0:00-24:00）。
      tags:
        - 课程管理 - 校长端
      security:
        - bearerAuth: []
      parameters:
        - name: teacherId
          in: query
          required: true
          description: 教师ID
          schema:
            type: integer
            format: int64
            example: 12345
        - name: classroomId
          in: query
          required: true
          description: 教室ID
          schema:
            type: integer
            format: int64
            example: 67890
        - name: startDate
          in: query
          required: true
          description: 开始日期（YYYY-MM-DD格式）
          schema:
            type: string
            format: date
            example: "2024-01-15"
        - name: endDate
          in: query
          required: true
          description: 结束日期（YYYY-MM-DD格式）
          schema:
            type: string
            format: date
            example: "2024-01-21"
        - name: scheduleMode
          in: query
          required: false
          description: |
            排课模式：
            - standard: 标准模式（8:00-12:00, 14:00-18:00）
            - extended: 24小时模式（0:00-24:00）
          schema:
            type: string
            enum: [standard, extended]
            default: standard
            example: "extended"
      responses:
        200:
          description: 获取空闲时段成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "获取空闲时段成功"
                  data:
                    type: object
                    description: 按日期分组的空闲时段列表
                    additionalProperties:
                      type: array
                      items:
                        $ref: '#/components/schemas/TimeSlot'
                    example:
                      "2024-01-15":
                        - startTime: "08:00:00"
                          endTime: "08:30:00"
                          available: true
                        - startTime: "08:30:00"
                          endTime: "09:00:00"
                          available: true
                      "2024-01-16":
                        - startTime: "00:00:00"
                          endTime: "00:30:00"
                          available: true
                        - startTime: "00:30:00"
                          endTime: "01:00:00"
                          available: true
        400:
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 400
                message: "参数不能为空"
                data: null
        401:
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 401
                message: "未授权访问"
                data: null
        403:
          description: 权限不足
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 403
                message: "权限不足"
                data: null
        500:
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 500
                message: "服务器内部错误"
                data: null

components:
  schemas:
    TimeSlot:
      type: object
      description: 时间段信息
      properties:
        startTime:
          type: string
          format: time
          description: 开始时间（HH:mm:ss格式）
          example: "08:00:00"
        endTime:
          type: string
          format: time
          description: 结束时间（HH:mm:ss格式）
          example: "08:30:00"
        available:
          type: boolean
          description: 是否可用
          example: true
      required:
        - startTime
        - endTime
        - available

    ErrorResponse:
      type: object
      description: 错误响应格式
      properties:
        code:
          type: integer
          description: 错误码
          example: 400
        message:
          type: string
          description: 错误信息
          example: "参数不能为空"
        data:
          type: object
          nullable: true
          description: 错误详情数据
          example: null
      required:
        - code
        - message

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT认证，在请求头中添加 Authorization: Bearer {token}

tags:
  - name: 课程管理 - 校长端
    description: 校长端课程管理相关接口

# 更新说明
x-changelog:
  version: "1.1.0"
  date: "2024-01-15"
  changes:
    - type: "feature"
      description: "新增 scheduleMode 参数支持24小时排课模式"
      details:
        - "添加 scheduleMode 查询参数（可选）"
        - "支持 standard 标准模式（8:00-12:00, 14:00-18:00）"
        - "支持 extended 24小时模式（0:00-24:00）"
        - "默认值为 standard 保持向后兼容"
    - type: "improvement"
      description: "增强排课灵活性，支持全天候课程安排"
    - type: "documentation"
      description: "更新接口文档，包含详细的参数说明和示例"

# 使用示例
x-examples:
  - name: "标准模式获取空闲时段"
    description: "使用标准工作时间模式获取一周的空闲时段"
    request:
      method: GET
      url: "/principal/courses/available-slots?teacherId=12345&classroomId=67890&startDate=2024-01-15&endDate=2024-01-21&scheduleMode=standard"
      headers:
        Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    
  - name: "24小时模式获取空闲时段"
    description: "使用24小时模式获取一周的空闲时段，支持全天候排课"
    request:
      method: GET
      url: "/principal/courses/available-slots?teacherId=12345&classroomId=67890&startDate=2024-01-15&endDate=2024-01-21&scheduleMode=extended"
      headers:
        Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

  - name: "默认模式（向后兼容）"
    description: "不传递 scheduleMode 参数时，默认使用标准模式"
    request:
      method: GET
      url: "/principal/courses/available-slots?teacherId=12345&classroomId=67890&startDate=2024-01-15&endDate=2024-01-21"
      headers:
        Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."