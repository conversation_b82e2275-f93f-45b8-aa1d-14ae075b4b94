<template>
  <div class="schedule-container">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧侧边栏 -->
      <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
        <div class="sidebar-header">
          <div class="view-toggle">
            <el-button-group>
              <el-button
                :type="viewMode === 'view' ? 'primary' : 'default'"
                @click="switchViewMode('view')"
                size="small"
              >
                <el-icon><View /></el-icon>
                查看排课
              </el-button>
              <el-button
                :type="viewMode === 'add' ? 'primary' : 'default'"
                @click="switchViewMode('add')"
                size="small"
              >
                <el-icon><Plus /></el-icon>
                新增排课
              </el-button>
            </el-button-group>
          </div>
          <div class="header-actions">
            <el-button
              @click="openScheduleQueryDialog"
              type="primary"
              size="small"
              plain
            >
              <el-icon><Search /></el-icon>
              排课查询
            </el-button>
            <el-button
              @click="toggleSidebar"
              type="text"
              class="collapse-btn"
            >
              <el-icon><Fold v-if="!sidebarCollapsed" /><Expand v-else /></el-icon>
            </el-button>
          </div>
        </div>

        <div class="sidebar-content" v-show="!sidebarCollapsed">
          <!-- 教室筛选器 -->
          <div class="filter-section">
            <h4>教室筛选</h4>
            <el-select
              v-model="selectedClassroomId"
              placeholder="请选择教室"
              clearable
              @change="handleClassroomChange"
              style="width: 100%"
            >
              <el-option
                v-for="classroom in classrooms"
                :key="classroom.classroomId"
                :label="classroom.name"
                :value="classroom.classroomId"
              />
            </el-select>
          </div>

          <!-- 学科筛选器 -->
          <div class="filter-section">
            <h4>学科筛选</h4>
            <el-select
              v-model="selectedSubject"
              placeholder="请选择学科"
              clearable
              @change="handleSubjectChange"
              style="width: 100%"
            >
              <el-option
                v-for="subject in subjects"
                :key="subject"
                :label="subject"
                :value="subject"
              />
            </el-select>
          </div>

          <!-- 教师筛选器 -->
          <div class="filter-section">
            <h4>教师筛选</h4>
            <el-select
              v-model="selectedTeacherId"
              placeholder="请选择教师"
              clearable
              @change="handleTeacherChange"
              style="width: 100%"
            >
              <el-option
                v-for="teacher in teachers"
                :key="teacher.userId"
                :label="teacher.realName"
                :value="teacher.userId"
              />
            </el-select>
          </div>

          <!-- 周次选择 -->
          <div class="filter-section">
            <h4>周次选择</h4>
            <el-date-picker
              v-model="selectedWeek"
              type="week"
              placeholder="选择周次"
              format="YYYY年第WW周"
              value-format="YYYY-MM-DD"
              @change="handleWeekChange"
              style="width: 100%"
            />
          </div>

          <!-- 排课模式选择 -->
          <div class="filter-section">
            <h4>排课模式</h4>
            <el-radio-group
              v-model="scheduleMode"
              @change="handleScheduleModeChange"
              style="width: 100%"
            >
              <el-radio value="standard" style="width: 100%; margin-bottom: 8px;">
                <div class="mode-option">
                  <div class="mode-title">标准模式</div>
                  <div class="mode-desc">8:00-12:00，14:00-18:00</div>
                </div>
              </el-radio>
              <el-radio value="extended" style="width: 100%;">
                <div class="mode-option">
                  <div class="mode-title">24小时模式</div>
                  <div class="mode-desc">全天候排课 0:00-24:00</div>
                </div>
              </el-radio>
            </el-radio-group>
          </div>

          <!-- 新增排课模式下的提示 -->
          <div v-if="viewMode === 'add'" class="add-mode-tips">
            <el-alert
              title="新增排课模式"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <p>请先选择教室和教师，然后点击右侧绿色区域进行排课</p>
              </template>
            </el-alert>
          </div>
        </div>
      </div>

      <!-- 右侧日历视图 -->
      <div class="calendar-container">
        <div class="calendar-header">
          <div class="week-navigation">
            <el-button @click="previousWeek" type="text">
              <el-icon><ArrowLeft /></el-icon>
            </el-button>
            <span class="week-display">{{ weekDisplayText }}</span>
            <el-button @click="nextWeek" type="text">
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
          <div class="current-time-indicator" v-if="showCurrentTime">
            <span>当前时间: {{ currentTimeText }}</span>
          </div>
        </div>

        <div class="calendar-header-row">
          <!-- 时间轴头部 -->
          <div class="time-axis-header">
            <div class="time-header"></div>
          </div>
          <!-- 日期头部行 -->
          <div
            v-for="(day, dayIndex) in weekDays"
            :key="day.date"
            class="day-header"
          >
            <div class="day-name">{{ day.dayName }}</div>
            <div class="day-date">{{ day.dateDisplay }}</div>
          </div>
        </div>

        <div class="calendar-grid" v-loading="loading">
          <!-- 时间轴 -->
          <div class="time-axis">
            <div
              v-for="timeSlot in timeSlots"
              :key="timeSlot.start"
              class="time-slot"
              :style="{ height: timeSlotHeight + 'px' }"
            >
              {{ timeSlot.display }}
            </div>
          </div>

          <!-- 日期列 -->
          <div
            v-for="(day, dayIndex) in weekDays"
            :key="day.date"
            class="day-column"
          >
            <!-- 时间段网格 -->
            <div class="time-slots">
              <div
                v-for="(timeSlot, timeIndex) in timeSlots"
                :key="timeSlot.start"
                class="time-slot-cell"
                :class="{
                  'available': isTimeSlotAvailable(day.date, timeSlot) && viewMode === 'add',
                  'occupied': isTimeSlotOccupied(day.date, timeSlot),
                  'current-time': isCurrentTimeSlot(day.date, timeSlot)
                }"
                :style="{ height: timeSlotHeight + 'px' }"
                @click="handleTimeSlotClick(day, timeSlot, dayIndex, timeIndex)"
              >
                <!-- 现有课程 -->
                <div
                  v-for="course in getCoursesInTimeSlot(day.date, timeSlot)"
                  :key="course.courseId"
                  class="course-block"
                  :style="getCourseBlockStyle(course)"
                  @click.stop="handleCourseClick(course)"
                >
                  <div class="course-title">{{ course.gradeLevel || '课程' }}</div>
                  <div class="course-teacher">{{ course.teacher?.realName }}</div>
                  <div class="course-student">{{ course.student?.name }}</div>
                  <div class="course-time">
                    {{ formatTime(course.startTime) }} - {{ formatTime(course.endTime) }}
                  </div>
                </div>

                <!-- 空闲时段提示 -->
                <div
                  v-if="isTimeSlotAvailable(day.date, timeSlot) && viewMode === 'add' && !getCoursesInTimeSlot(day.date, timeSlot).length"
                  class="available-slot-hint"
                >
                  点击排课
                </div>
              </div>
            </div>

            <!-- 当前时间线 -->
            <div
              v-if="isToday(day.date)"
              class="current-time-line"
              :style="{ top: getCurrentTimePosition() + 'px' }"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增排课对话框 -->
    <el-dialog
      v-model="addCourseDialogVisible"
      title="新增排课"
      width="600px"
      @close="handleAddCourseDialogClose"
    >
      <el-form
        ref="addCourseFormRef"
        :model="addCourseForm"
        :rules="addCourseRules"
        label-width="100px"
      >
        <el-form-item label="上课日期">
          <el-input :value="addCourseForm.dateDisplay" disabled />
        </el-form-item>

        <el-form-item label="时间段" prop="timeRange">
          <el-time-picker
            v-model="addCourseForm.timeRange"
            is-range
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="HH:mm"
            value-format="HH:mm:ss"
            :disabled-hours="getDisabledHours"
            :disabled-minutes="getDisabledMinutes"
          />
        </el-form-item>

        <el-form-item label="教师" prop="teacherId">
          <el-select
            v-model="addCourseForm.teacherId"
            placeholder="请选择教师"
            style="width: 100%"
            :disabled="!!selectedTeacherId"
          >
            <el-option
              v-for="teacher in teachers"
              :key="teacher.userId"
              :label="teacher.realName"
              :value="teacher.userId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="教室" prop="classroomId">
          <el-select
            v-model="addCourseForm.classroomId"
            placeholder="请选择教室"
            style="width: 100%"
            :disabled="!!selectedClassroomId"
          >
            <el-option
              v-for="classroom in classrooms"
              :key="classroom.classroomId"
              :label="classroom.name"
              :value="classroom.classroomId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="学生" prop="studentIds">
          <el-select
            v-model="addCourseForm.studentIds"
            placeholder="请选择学生"
            multiple
            filterable
            style="width: 100%"
            @change="handleStudentChange"
          >
            <el-option
              v-for="student in students"
              :key="student.studentId"
              :label="student.name"
              :value="student.studentId"
            />
          </el-select>
          <div v-if="addCourseForm.studentIds.length > 0" class="student-count">
            已选择 {{ addCourseForm.studentIds.length }} 名学生
          </div>
        </el-form-item>

        <el-form-item label="课程价格" prop="price">
          <el-input-number
            v-model="addCourseForm.price"
            :min="0"
            :precision="2"
            placeholder="请输入课程价格"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="年级" prop="gradeLevel">
          <el-input v-model="addCourseForm.gradeLevel" placeholder="请输入年级" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addCourseDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAddCourseSubmit" :loading="submitting">
            确定排课
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看/编辑课程对话框 -->
    <el-dialog
      v-model="viewCourseDialogVisible"
      :title="viewCourseDialogTitle"
      width="500px"
      @close="handleViewCourseDialogClose"
    >
      <div v-if="currentCourse" class="course-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="上课日期">
            {{ formatDate(currentCourse.courseDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="上课时间">
            {{ formatTime(currentCourse.startTime) }} - {{ formatTime(currentCourse.endTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="教师">
            {{ currentCourse.teacher?.realName }}
          </el-descriptions-item>
          <el-descriptions-item label="教室">
            {{ currentCourse.classroom?.name }}
          </el-descriptions-item>
          <el-descriptions-item label="学生">
            {{ currentCourse.student?.name }}
          </el-descriptions-item>
          <el-descriptions-item label="课程价格">
            ¥{{ currentCourse.price }}
          </el-descriptions-item>
          <el-descriptions-item label="年级">
            {{ currentCourse.gradeLevel }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getCourseStatusType(currentCourse.status)">
              {{ getCourseStatusText(currentCourse.status) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="viewCourseDialogVisible = false">关闭</el-button>
          <el-button type="warning" @click="handleEditCourse" v-if="currentCourse?.status === 'scheduled'">
            编辑
          </el-button>
          <el-button type="danger" @click="handleDeleteCourse" v-if="currentCourse?.status === 'scheduled'">
            删除
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 排课查询对话框 -->
    <el-dialog
      v-model="scheduleQueryDialogVisible"
      title="排课查询"
      width="600px"
      @close="handleScheduleQueryDialogClose"
    >
      <el-form
        ref="scheduleQueryFormRef"
        :model="scheduleQueryForm"
        :rules="scheduleQueryRules"
        label-width="100px"
      >
        <el-form-item label="日期选择" prop="courseDate">
          <el-date-picker
            v-model="scheduleQueryForm.courseDate"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleQueryDateChange"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="时间选择" prop="timeRange">
          <el-time-picker
            v-model="scheduleQueryForm.timeRange"
            is-range
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="HH:mm"
            value-format="HH:mm:ss"
            @change="handleQueryTimeChange"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="教室选择" prop="classroomId">
          <el-select
            v-model="scheduleQueryForm.classroomId"
            placeholder="请选择教室"
            style="width: 100%"
            @change="handleQueryClassroomChange"
            :loading="loadingAvailableClassrooms"
          >
            <el-option
              v-for="classroom in availableClassrooms"
              :key="classroom.classroomId"
              :label="classroom.name"
              :value="classroom.classroomId"
            />
          </el-select>
          <div v-if="availableClassrooms.length === 0 && scheduleQueryForm.timeRange.length === 2" class="query-tip error">
            当前所选时间段无空闲教室
          </div>
        </el-form-item>

        <el-form-item label="学科选择" prop="subject">
          <el-select
            v-model="scheduleQueryForm.subject"
            placeholder="请选择学科"
            style="width: 100%"
            @change="handleQuerySubjectChange"
            :disabled="!scheduleQueryForm.classroomId"
          >
            <el-option
              v-for="subject in subjects"
              :key="subject"
              :label="subject"
              :value="subject"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="教师选择" prop="teacherId">
          <el-select
            v-model="scheduleQueryForm.teacherId"
            placeholder="请选择教师"
            style="width: 100%"
            :disabled="!scheduleQueryForm.subject"
            :loading="loadingAvailableTeachers"
          >
            <el-option
              v-for="teacher in availableTeachers"
              :key="teacher.userId"
              :label="teacher.realName"
              :value="teacher.userId"
            />
          </el-select>
          <div v-if="availableTeachers.length === 0 && scheduleQueryForm.subject" class="query-tip error">
            当前所选教室和时间无空闲老师
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="scheduleQueryDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleScheduleQuerySubmit"
            :disabled="!isQueryFormValid"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, View, Fold, Expand, ArrowLeft, ArrowRight, Search
} from '@element-plus/icons-vue'
import { principalCourseApi } from '@/api/course'
import { principalStudentApi } from '@/api/student'
import { principalClassroomApi } from '@/api/classroom'
import { principalTeacherApi } from '@/api/teacher'
import { AVAILABLE_SUBJECTS } from '@/constants/subjects'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const sidebarCollapsed = ref(false)
const viewMode = ref('view') // 'view' | 'add'

// 选择的筛选条件
const selectedClassroomId = ref(null)
const selectedSubject = ref('')
const selectedTeacherId = ref(null)
const selectedWeek = ref('')
const scheduleMode = ref('standard') // 排课模式：standard | extended

// 学科列表
const subjects = ref([...AVAILABLE_SUBJECTS])

// 数据列表
const classrooms = ref([])
const teachers = ref([])
const students = ref([])
const courses = ref([])
const availableSlots = ref({})

// 对话框状态
const addCourseDialogVisible = ref(false)
const viewCourseDialogVisible = ref(false)
const scheduleQueryDialogVisible = ref(false)
const addCourseFormRef = ref()
const scheduleQueryFormRef = ref()
const currentCourse = ref(null)

// 新增排课表单
const addCourseForm = reactive({
  courseDate: '',
  dateDisplay: '',
  timeRange: [],
  teacherId: null,
  classroomId: null,
  studentIds: [],
  price: 0,
  gradeLevel: ''
})

// 排课查询表单
const scheduleQueryForm = reactive({
  courseDate: '',
  timeRange: [],
  classroomId: null,
  subject: '',
  teacherId: null
})

// 查询相关数据
const availableClassrooms = ref([])
const availableTeachers = ref([])
const loadingAvailableClassrooms = ref(false)
const loadingAvailableTeachers = ref(false)

// 表单验证规则
const addCourseRules = {
  timeRange: [
    { required: true, message: '请选择时间段', trigger: 'change' }
  ],
  teacherId: [
    { required: true, message: '请选择教师', trigger: 'change' }
  ],
  classroomId: [
    { required: true, message: '请选择教室', trigger: 'change' }
  ],
  studentIds: [
    { required: true, message: '请选择至少一名学生', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入课程价格', trigger: 'blur' }
  ]
}

// 排课查询表单验证规则
const scheduleQueryRules = {
  courseDate: [
    { required: true, message: '请选择日期', trigger: 'change' }
  ],
  timeRange: [
    { required: true, message: '请选择时间段', trigger: 'change' }
  ]
}

// 时间配置
const timeSlotHeight = 60 // 每个时间段的高度(px)
const timeSlots = ref([])

// 生成时间段（30分钟间隔）- 24小时制
const generateTimeSlots = () => {
  const slots = []
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const start = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
      const endMinute = minute + 30
      const endHour = endMinute >= 60 ? hour + 1 : hour
      const end = `${endHour.toString().padStart(2, '0')}:${(endMinute % 60).toString().padStart(2, '0')}`

      slots.push({
        start,
        end,
        display: start
      })
    }
  }
  return slots
}

// 计算属性
const weekDays = computed(() => {
  if (!selectedWeek.value) return []

  const startDate = new Date(selectedWeek.value)
  const days = []

  for (let i = 0; i < 7; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)

    const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

    days.push({
      date: date.toISOString().split('T')[0],
      dayName: dayNames[date.getDay()],
      dateDisplay: `${date.getMonth() + 1}/${date.getDate()}`
    })
  }

  return days
})

const weekDisplayText = computed(() => {
  if (!selectedWeek.value || weekDays.value.length === 0) return ''

  const startDate = weekDays.value[0].dateDisplay
  const endDate = weekDays.value[6].dateDisplay
  return `${startDate} - ${endDate}`
})

const currentTimeText = computed(() => {
  const now = new Date()
  return now.toLocaleTimeString('zh-CN', { hour12: false })
})

const showCurrentTime = computed(() => {
  return weekDays.value.some(day => isToday(day.date))
})

const viewCourseDialogTitle = computed(() => {
  return currentCourse.value ? '课程详情' : '课程信息'
})

const isQueryFormValid = computed(() => {
  return scheduleQueryForm.courseDate &&
         scheduleQueryForm.timeRange.length === 2 &&
         scheduleQueryForm.classroomId &&
         scheduleQueryForm.subject &&
         scheduleQueryForm.teacherId
})

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const switchViewMode = (mode) => {
  viewMode.value = mode
  if (mode === 'add') {
    fetchAvailableSlots()
  }
}

const fetchClassrooms = async () => {
  try {
    const response = await principalClassroomApi.getAll()
    classrooms.value = response.data || []

    // 默认选择第一个教室
    if (classrooms.value.length > 0 && !selectedClassroomId.value) {
      selectedClassroomId.value = classrooms.value[0].classroomId
    }
  } catch (error) {
    ElMessage.error('获取教室列表失败')
  }
}

const fetchTeachers = async () => {
  try {
    const response = await principalTeacherApi.getAll(selectedSubject.value)
    teachers.value = response.data || []

    // 如果当前选中的教师不在新的列表中，清空选择
    if (selectedTeacherId.value && !teachers.value.find(t => t.userId === selectedTeacherId.value)) {
      selectedTeacherId.value = null
    }

    // 默认选择第一个教师
    if (teachers.value.length > 0 && !selectedTeacherId.value) {
      selectedTeacherId.value = teachers.value[0].userId
    }
  } catch (error) {
    ElMessage.error('获取教师列表失败')
  }
}

const fetchStudents = async () => {
  try {
    const response = await principalStudentApi.getAll()
    students.value = response.data || []
  } catch (error) {
    ElMessage.error('获取学生列表失败')
  }
}

const fetchCourses = async () => {
  if (!selectedWeek.value) return

  loading.value = true
  try {
    const startDate = selectedWeek.value
    const endDate = new Date(selectedWeek.value)
    endDate.setDate(endDate.getDate() + 6)

    const response = await principalCourseApi.getByDateRange({
      startDate,
      endDate: endDate.toISOString().split('T')[0],
      teacherId: selectedTeacherId.value,
      classroomId: selectedClassroomId.value
    })

    courses.value = response.data || []
  } catch (error) {
    ElMessage.error('获取课程数据失败')
  } finally {
    loading.value = false
  }
}

const fetchAvailableSlots = async () => {
  if (!selectedTeacherId.value || !selectedClassroomId.value || !selectedWeek.value) {
    availableSlots.value = {}
    return
  }

  try {
    const startDate = selectedWeek.value
    const endDate = new Date(selectedWeek.value)
    endDate.setDate(endDate.getDate() + 6)

    const response = await principalCourseApi.getAvailableSlots({
      teacherId: selectedTeacherId.value,
      classroomId: selectedClassroomId.value,
      startDate,
      endDate: endDate.toISOString().split('T')[0],
      scheduleMode: scheduleMode.value
    })

    availableSlots.value = response.data || {}
  } catch (error) {
    ElMessage.error('获取空闲时段失败')
  }
}

// 事件处理
const handleClassroomChange = () => {
  fetchCourses()
  if (viewMode.value === 'add') {
    fetchAvailableSlots()
  }
}

const handleSubjectChange = () => {
  fetchTeachers()
}

const handleTeacherChange = () => {
  fetchCourses()
  if (viewMode.value === 'add') {
    fetchAvailableSlots()
  }
}

const handleWeekChange = () => {
  fetchCourses()
  if (viewMode.value === 'add') {
    fetchAvailableSlots()
  }
}

const handleScheduleModeChange = () => {
  if (viewMode.value === 'add') {
    fetchAvailableSlots()
  }
}

const previousWeek = () => {
  if (!selectedWeek.value) return

  const currentDate = new Date(selectedWeek.value)
  currentDate.setDate(currentDate.getDate() - 7)
  selectedWeek.value = currentDate.toISOString().split('T')[0]
  handleWeekChange()
}

const nextWeek = () => {
  if (!selectedWeek.value) return

  const currentDate = new Date(selectedWeek.value)
  currentDate.setDate(currentDate.getDate() + 7)
  selectedWeek.value = currentDate.toISOString().split('T')[0]
  handleWeekChange()
}

const handleTimeSlotClick = (day, timeSlot, dayIndex, timeIndex) => {
  if (viewMode.value !== 'add') return

  if (!selectedTeacherId.value || !selectedClassroomId.value) {
    ElMessage.warning('请先选择教师和教室')
    return
  }

  if (!isTimeSlotAvailable(day.date, timeSlot)) {
    ElMessage.warning('该时间段不可用')
    return
  }

  // 打开新增排课对话框
  openAddCourseDialog(day, timeSlot)
}

const openAddCourseDialog = (day, timeSlot) => {
  // 重置表单
  Object.assign(addCourseForm, {
    courseDate: day.date,
    dateDisplay: `${day.dayName} ${day.dateDisplay}`,
    timeRange: [timeSlot.start + ':00', timeSlot.end + ':00'],
    teacherId: selectedTeacherId.value,
    classroomId: selectedClassroomId.value,
    studentIds: [],
    price: 0,
    gradeLevel: ''
  })

  addCourseDialogVisible.value = true
}

const handleCourseClick = (course) => {
  currentCourse.value = course
  viewCourseDialogVisible.value = true
}

const handleStudentChange = () => {
  // 可以在这里添加学生选择变化的逻辑
}

const handleAddCourseSubmit = async () => {
  try {
    await addCourseFormRef.value.validate()

    if (addCourseForm.studentIds.length === 0) {
      ElMessage.warning('请至少选择一名学生')
      return
    }

    submitting.value = true

    // 为每个学生创建一个课程
    const promises = addCourseForm.studentIds.map(studentId => {
      return principalCourseApi.create({
        teacherId: addCourseForm.teacherId,
        studentId,
        classroomId: addCourseForm.classroomId,
        courseDate: addCourseForm.courseDate,
        startTime: addCourseForm.timeRange[0],
        endTime: addCourseForm.timeRange[1],
        price: addCourseForm.price,
        gradeLevel: addCourseForm.gradeLevel
      })
    })

    await Promise.all(promises)

    ElMessage.success('排课成功')
    addCourseDialogVisible.value = false
    fetchCourses()
    if (viewMode.value === 'add') {
      fetchAvailableSlots()
    }
  } catch (error) {
    ElMessage.error('排课失败')
  } finally {
    submitting.value = false
  }
}

const handleEditCourse = () => {
  // TODO: 实现编辑课程功能
  ElMessage.info('编辑功能待开发')
}

const handleDeleteCourse = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个课程吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await principalCourseApi.delete(currentCourse.value.courseId)
    ElMessage.success('删除成功')
    viewCourseDialogVisible.value = false
    fetchCourses()
    if (viewMode.value === 'add') {
      fetchAvailableSlots()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleAddCourseDialogClose = () => {
  addCourseFormRef.value?.resetFields()
}

const handleViewCourseDialogClose = () => {
  currentCourse.value = null
}

// 排课查询相关方法
const openScheduleQueryDialog = () => {
  // 重置表单
  Object.assign(scheduleQueryForm, {
    courseDate: new Date().toISOString().split('T')[0], // 默认今天
    timeRange: [],
    classroomId: null,
    subject: '',
    teacherId: null
  })

  // 清空查询结果
  availableClassrooms.value = []
  availableTeachers.value = []

  scheduleQueryDialogVisible.value = true
}

const handleScheduleQueryDialogClose = () => {
  scheduleQueryFormRef.value?.resetFields()
  availableClassrooms.value = []
  availableTeachers.value = []
}

const handleQueryDateChange = () => {
  // 日期变化时重置后续选择
  scheduleQueryForm.timeRange = []
  scheduleQueryForm.classroomId = null
  scheduleQueryForm.subject = ''
  scheduleQueryForm.teacherId = null
  availableClassrooms.value = []
  availableTeachers.value = []
}

const handleQueryTimeChange = () => {
  // 时间变化时重置后续选择
  scheduleQueryForm.classroomId = null
  scheduleQueryForm.subject = ''
  scheduleQueryForm.teacherId = null
  availableTeachers.value = []

  // 查询空闲教室
  if (scheduleQueryForm.courseDate && scheduleQueryForm.timeRange.length === 2) {
    fetchAvailableClassrooms()
  }
}

const handleQueryClassroomChange = () => {
  // 教室变化时重置后续选择
  scheduleQueryForm.subject = ''
  scheduleQueryForm.teacherId = null
  availableTeachers.value = []
}

const handleQuerySubjectChange = () => {
  // 学科变化时重置教师选择
  scheduleQueryForm.teacherId = null

  // 查询空闲教师
  if (scheduleQueryForm.courseDate &&
      scheduleQueryForm.timeRange.length === 2 &&
      scheduleQueryForm.classroomId &&
      scheduleQueryForm.subject) {
    fetchAvailableTeachers()
  }
}

const fetchAvailableClassrooms = async () => {
  if (!scheduleQueryForm.courseDate || scheduleQueryForm.timeRange.length !== 2) {
    return
  }

  loadingAvailableClassrooms.value = true
  try {
    const response = await principalClassroomApi.getAvailableClassrooms({
      courseDate: scheduleQueryForm.courseDate,
      startTime: scheduleQueryForm.timeRange[0],
      endTime: scheduleQueryForm.timeRange[1]
    })
    availableClassrooms.value = response.data || []
  } catch (error) {
    ElMessage.error('获取空闲教室失败')
    availableClassrooms.value = []
  } finally {
    loadingAvailableClassrooms.value = false
  }
}

const fetchAvailableTeachers = async () => {
  if (!scheduleQueryForm.courseDate ||
      scheduleQueryForm.timeRange.length !== 2 ||
      !scheduleQueryForm.subject) {
    return
  }

  loadingAvailableTeachers.value = true
  try {
    const response = await principalTeacherApi.getAvailableTeachers({
      courseDate: scheduleQueryForm.courseDate,
      startTime: scheduleQueryForm.timeRange[0],
      endTime: scheduleQueryForm.timeRange[1],
      subject: scheduleQueryForm.subject
    })
    availableTeachers.value = response.data || []
  } catch (error) {
    ElMessage.error('获取空闲教师失败')
    availableTeachers.value = []
  } finally {
    loadingAvailableTeachers.value = false
  }
}

const handleScheduleQuerySubmit = () => {
  if (!isQueryFormValid.value) {
    ElMessage.warning('请完成所有必填项')
    return
  }

  // 将查询结果应用到主页面筛选条件
  selectedClassroomId.value = scheduleQueryForm.classroomId
  selectedSubject.value = scheduleQueryForm.subject
  selectedTeacherId.value = scheduleQueryForm.teacherId

  // 设置周次为查询日期所在的周
  const queryDate = new Date(scheduleQueryForm.courseDate)
  const monday = new Date(queryDate)
  monday.setDate(queryDate.getDate() - queryDate.getDay() + 1)
  selectedWeek.value = monday.toISOString().split('T')[0]

  // 切换到新增排课模式
  viewMode.value = 'add'

  // 关闭对话框
  scheduleQueryDialogVisible.value = false

  // 刷新数据
  fetchCourses()
  fetchAvailableSlots()

  ElMessage.success('查询条件已应用，已切换到新增排课模式')
}

// 工具方法
const isTimeSlotAvailable = (date, timeSlot) => {
  if (viewMode.value !== 'add') return false

  const daySlots = availableSlots.value[date]
  if (!daySlots) return false

  return daySlots.some(slot =>
    slot.startTime === timeSlot.start + ':00' &&
    slot.endTime === timeSlot.end + ':00' &&
    slot.available
  )
}

const isTimeSlotOccupied = (date, timeSlot) => {
  return getCoursesInTimeSlot(date, timeSlot).length > 0
}

const getCoursesInTimeSlot = (date, timeSlot) => {
  return courses.value.filter(course => {
    if (course.courseDate !== date) return false

    const courseStart = course.startTime.substring(0, 5)
    const courseEnd = course.endTime.substring(0, 5)

    // 检查时间段是否重叠
    return !(timeSlot.end <= courseStart || timeSlot.start >= courseEnd)
  })
}

const getCourseBlockStyle = (course) => {
  // 计算课程块的样式
  const startTime = course.startTime.substring(0, 5)
  const endTime = course.endTime.substring(0, 5)

  const startMinutes = timeToMinutes(startTime)
  const endMinutes = timeToMinutes(endTime)
  const duration = endMinutes - startMinutes

  const firstSlotStart = timeToMinutes('00:00') // 24小时制从0点开始
  const topOffset = ((startMinutes - firstSlotStart) / 30) * timeSlotHeight
  const height = (duration / 30) * timeSlotHeight

  return {
    position: 'absolute',
    top: topOffset + 'px',
    height: height + 'px',
    width: '100%',
    zIndex: 10
  }
}

const timeToMinutes = (timeStr) => {
  const [hours, minutes] = timeStr.split(':').map(Number)
  return hours * 60 + minutes
}

const isCurrentTimeSlot = (date, timeSlot) => {
  if (!isToday(date)) return false

  const now = new Date()
  const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`

  return currentTime >= timeSlot.start && currentTime < timeSlot.end
}

const isToday = (date) => {
  const today = new Date().toISOString().split('T')[0]
  return date === today
}

const getCurrentTimePosition = () => {
  const now = new Date()
  const currentMinutes = now.getHours() * 60 + now.getMinutes()
  const firstSlotStart = 0 * 60 // 00:00 (24小时制从0点开始)

  if (currentMinutes < firstSlotStart) return 0

  return ((currentMinutes - firstSlotStart) / 30) * timeSlotHeight
}

const formatTime = (timeStr) => {
  return timeStr ? timeStr.substring(0, 5) : ''
}

const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN')
}

const getCourseStatusType = (status) => {
  const statusMap = {
    'scheduled': '',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return statusMap[status] || ''
}

const getCourseStatusText = (status) => {
  const statusMap = {
    'scheduled': '已排课',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

const getDisabledHours = () => {
  // 24小时制，不限制时间选择范围
  return []
}

const getDisabledMinutes = (hour) => {
  // 只允许整点和半点
  const disabled = []
  for (let i = 1; i < 60; i++) {
    if (i !== 30) {
      disabled.push(i)
    }
  }
  return disabled
}

// 初始化
const init = async () => {
  // 生成时间段
  timeSlots.value = generateTimeSlots()
  
  // 调试信息：检查生成的时间段
  console.log('生成的时间段数量:', timeSlots.value.length)
  console.log('第一个时间段:', timeSlots.value[0])
  console.log('最后一个时间段:', timeSlots.value[timeSlots.value.length - 1])
  console.log('所有时间段:', timeSlots.value.map(slot => slot.display))

  // 设置默认周次为当前周
  const now = new Date()
  const monday = new Date(now)
  monday.setDate(now.getDate() - now.getDay() + 1)
  selectedWeek.value = monday.toISOString().split('T')[0]

  // 设置默认学科为第一个
  if (subjects.value.length > 0) {
    selectedSubject.value = subjects.value[0]
  }

  // 获取基础数据
  await Promise.all([
    fetchClassrooms(),
    fetchTeachers(),
    fetchStudents()
  ])

  // 获取课程数据
  await fetchCourses()
}

// 生命周期
onMounted(() => {
  init()
})
</script>

<style scoped>
.schedule-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 侧边栏样式 */
.sidebar {
  width: 280px;
  background: #fff;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
}

.sidebar.collapsed {
  width: 60px;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.view-toggle {
  flex: 1;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.collapse-btn {
  margin-left: 8px;
}

.sidebar-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 24px;
}

.filter-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.add-mode-tips {
  margin-top: 24px;
}

/* 排课模式选择器样式 */
.mode-option {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
}

.mode-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.mode-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
}

/* 日历容器样式 */
.calendar-container {
  flex: 1;
  background: #fff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.calendar-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.week-navigation {
  display: flex;
  align-items: center;
  gap: 16px;
}

.week-display {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  min-width: 120px;
  text-align: center;
}

.current-time-indicator {
  font-size: 14px;
  color: #909399;
}

/* 日历头部行样式 */
.calendar-header-row {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
  position: sticky;
  top: 0;
  z-index: 10;
}

.time-axis-header {
  width: 80px;
  border-right: 1px solid #e4e7ed;
}

.time-header {
  height: 60px;
  border-bottom: 1px solid #e4e7ed;
}

.day-header {
  flex: 1;
  height: 60px;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  min-width: 120px;
}

.day-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.day-date {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

/* 日历网格样式 */
.calendar-grid {
  flex: 1;
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;
  height: calc(100vh - 200px); /* 确保有足够的高度 */
  min-height: 600px; /* 最小高度 */
}

.time-axis {
  width: 80px;
  border-right: 1px solid #e4e7ed;
  background: #fafafa;
  position: sticky;
  left: 0;
  z-index: 5;
  min-height: 2880px; /* 48个时间段 × 60px = 2880px */
}

.time-axis .time-slot {
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #606266 !important; /* 确保颜色正确应用 */
  position: relative;
  background: #fafafa; /* 确保背景色 */
  box-sizing: border-box;
}

.day-column {
  flex: 1;
  border-right: 1px solid #e4e7ed;
  position: relative;
  min-width: 120px;
  min-height: 2880px; /* 确保日期列有足够高度 */
}

.time-slots {
  position: relative;
  min-height: 2880px; /* 确保时间槽容器有足够高度 */
}

.time-slot-cell {
  border-bottom: 1px solid #e4e7ed;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s ease;
  box-sizing: border-box;
}

.time-slot-cell:hover {
  background-color: #f5f7fa;
}

.time-slot-cell.available {
  background-color: rgba(103, 194, 58, 0.1);
  border: 1px dashed #67c23a;
}

.time-slot-cell.available:hover {
  background-color: rgba(103, 194, 58, 0.2);
}

.time-slot-cell.occupied {
  background-color: #f5f5f5;
}

.time-slot-cell.current-time {
  background-color: rgba(64, 158, 255, 0.1);
}

.available-slot-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  color: #67c23a;
  pointer-events: none;
}

/* 课程块样式 */
.course-block {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  color: white;
  border-radius: 6px;
  padding: 8px;
  margin: 2px;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  overflow: hidden;
}

.course-block:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.course-title {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.course-teacher {
  font-size: 11px;
  opacity: 0.9;
  margin-bottom: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.course-student {
  font-size: 11px;
  opacity: 0.9;
  margin-bottom: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.course-time {
  font-size: 10px;
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 当前时间线 */
.current-time-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background: #f56c6c;
  z-index: 100;
  box-shadow: 0 0 4px rgba(245, 108, 108, 0.5);
}

.current-time-line::before {
  content: '';
  position: absolute;
  left: -4px;
  top: -3px;
  width: 8px;
  height: 8px;
  background: #f56c6c;
  border-radius: 50%;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.student-count {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.course-details {
  margin-bottom: 16px;
}

.query-tip {
  margin-top: 8px;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
}

.query-tip.error {
  color: #f56c6c;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .sidebar {
    width: 240px;
  }

  .sidebar.collapsed {
    width: 50px;
  }

  .day-column {
    min-width: 100px;
  }
  
  .calendar-grid {
    height: calc(100vh - 180px);
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid #e4e7ed;
  }

  .sidebar.collapsed {
    width: 100%;
    height: 60px;
  }

  .calendar-grid {
    overflow-x: auto;
    height: calc(100vh - 300px); /* 移动端调整高度 */
    min-height: 400px;
  }

  .day-column {
    min-width: 80px;
    min-height: 2880px; /* 确保移动端也有足够高度 */
  }
  
  .time-axis {
    min-height: 2880px; /* 确保移动端时间轴有足够高度 */
  }
}
</style>
